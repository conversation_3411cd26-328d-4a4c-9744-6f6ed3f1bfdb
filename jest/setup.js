/* eslint-disable no-undef */
// jest/setup.js
import "@testing-library/jest-native/extend-expect";
import "react-native-gesture-handler/jestSetup";
jest.mock("react-native-track-player", () => ({
    useTrackPlayerEvents: jest.fn(),
    TrackPlayer: jest.fn(),
    setupPlayer: jest.fn(),
    updateOptions: jest.fn(),
    TrackType: jest.fn(),
    add: jest.fn(),
    play: jest.fn(),
    pause: jest.fn(),
}));

jest.mock("@react-native-community/netinfo", () => ({
    fetch: jest.fn(),
    NetInfo: jest.fn(),
    addEventListener: jest.fn(),
}));

jest.mock("react-native-safe-area-context", () => ({
    ...jest.requireActual("react-native-safe-area-context"),
    useSafeAreaInsets: jest
        .fn()
        .mockReturnValue({top: 20, bottom: 0, left: 0, right: 0}),
}));

jest.mock("react-native/Libraries/Alert/Alert", () => {
    return {
        alert: jest.fn(),
    };
});
