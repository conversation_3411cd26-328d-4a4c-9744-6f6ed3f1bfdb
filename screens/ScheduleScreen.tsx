import React, { useState, useEffect } from "react";
import {
    StatusBar,
    Dimensions,
    SafeAreaView,
    View,
    FlatList,
    StyleSheet,
    Text,
    Image,
    TouchableOpacity,
    Platform,
    ScrollView,
} from "react-native";

import { useNavigation, useRoute } from "@react-navigation/native";
import { ProgramItem } from "../src/interfaces/program";
import { SocialMediaView } from "./Home";
import { getData } from "../services/asyncStorageService";
import { ProgramConstants } from "../src/utils/constants";
import { useSafeAreaInsets } from "react-native-safe-area-context";

type NavigationProps = { navigation: any };
type ItemProps = {
    time: string;
    program: string;
    isHeading: boolean;
    data: string;
};
type PgmListProps = { programList: ProgramItem[] };

const ScheduleScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const insets = useSafeAreaInsets();

    const [programList, setProgramList] = useState<ProgramItem[]>([]);

    const items = route.params["programs"];

    useEffect(() => {
        setProgramList(items);
        return () => setProgramList([]);
    }, []);

    return (
        <View style={styles.container}>
            <StatusBar
                animated={true}
                backgroundColor="#DAD6C5"
                barStyle="light-content"
            />
            <Image
                style={styles.backgroundImage}
                source={require("../assets/enhancement/program_bg.jpg")}
            />
            <SafeAreaView style={[styles.safeAreaTop, { paddingTop: insets.top }]}>
                <View style={styles.backButtonContainer}>
                    <BackIcon navigation={navigation} />
                </View>
                <Image
                    style={styles.logo}
                    source={require("../assets/assets_img_logo.png")}
                />
            </SafeAreaView>

            <ScrollView contentContainerStyle={styles.scrollContent}>
                <PlayerList programList={programList} />
            </ScrollView>

            <View style={[styles.bottomContainer, { paddingBottom: insets.bottom + 10 }]}>
                <View style={styles.souqWaqifView}>
                    <Image
                        source={require("../assets/images/souq_waqif.png")}
                        style={styles.souqWaqifImage}
                        resizeMode="contain"
                    />
                </View>
                <SocialMediaView />
            </View>
        </View>
    );
};

const PlayerList = ({ programList }: PgmListProps) => {
    return (
        <View style={styles.playerListView}>
            {programList.length > 0 ? (
                <GridComponent programList={programList} />
            ) : (
                <EmptyView />
            )}
        </View>
    );
};

const GridComponent = ({ programList }: PgmListProps) => {
    const timeHeader = ProgramConstants.programDurationHead;
    const pgmHeader = ProgramConstants.programNameHead;
    return (
        <View style={{ flexDirection: "column" }}>
            <ListTileView
                time={timeHeader}
                program={pgmHeader}
                isHeading={true}
                data=""
            />
            <FlatList
                data={programList}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item }) => (
                    <ListTileView
                        time={item.program_endTime + " - " + item.program_startTime}
                        program={item.program_name}
                        isHeading={false}
                        data=""
                    />
                )}
            />
        </View>
    );
};

const ListTileView = ({ time, program, isHeading }: ItemProps) => (
    <View style={styles.playerListRow}>
        <ListTileItem time={time} program={""} isHeading={isHeading} data={time} />
        <ListTileItem time={""} program={program} isHeading={isHeading} data={program} />
    </View>
);

const ListTileItem = ({ data, isHeading }: ItemProps) => (
    <View style={isHeading ? styles.gridTitle : styles.playerListRowItem}>
        <Text style={isHeading ? styles.gridTitleText : styles.pgmNameAndTime}>
            {data}
        </Text>
    </View>
);

const EmptyView = () => (
    <View style={styles.emptyView}>
        <Text style={styles.pgmNameAndTime}>البرامج غير متوفرة للتاريخ الحالي</Text>
    </View>
);

const BackIcon = ({ navigation }: NavigationProps) => (
    <TouchableOpacity
        style={{ height: 50, width: 50 }}
        testID="BackIcon"
        onPress={() => navigation.goBack()}
    >
        <Image
            source={require("../assets/back.png")}
            style={{ height: 50, width: 50 }}
        />
    </TouchableOpacity>
);

const { width, height } = Dimensions.get("window");
const isIos = Platform.OS === "ios";

const styles = StyleSheet.create({
    container: {
        paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
        flex: 1,
        backgroundColor: "#fff",
    },
    backButtonContainer: {
        position: "absolute",
        left: 16,
        top: 0,
        zIndex: 10,
    },
    backgroundImage: {
        resizeMode: "stretch",
        width: "100%",
        height: "100%",
        ...StyleSheet.absoluteFillObject,
    },
    safeAreaTop: {
        alignItems: "center",
        zIndex: 1,
    },
    logo: {
        alignSelf: "center",
        width: "85%",
        height: height * 0.15,
        resizeMode: "contain",
    },
    pgmNameAndTime: {
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontSize: width * 0.04,
        fontWeight: isIos ? "400" : "800",
        color: "black",
        textAlign: "center",
        alignSelf: "center",
    },
    playerListView: {
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        marginHorizontal: 16,
        width: "100%",
    },
    scrollContent: {
        paddingBottom: 150, // Leaves space for footer
    },
    gridTitle: {
        backgroundColor: "#BF0F2E",
        padding: 8,
        margin: 5,
        alignItems: "center",
        borderRadius: 12,
        width: width / 2 - 32,
    },
    gridTitleText: {
        color: "rgb(255, 201, 47)",
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontSize: width * 0.05,
        fontWeight: isIos ? "400" : "800",
        textAlign: "center",
    },
    playerListRow: {
        flexDirection: "row",
        alignItems: "center",
    },
    playerListRowItem: {
        backgroundColor: "white",
        opacity: 0.6,
        padding: 10,
        margin: 5,
        borderRadius: 10,
        width: width / 2 - 32,
    },
    souqWaqifView: {
        width: "100%",
        alignItems: "center",
        paddingTop: 5
    },
    souqWaqifImage: {
        height: height * 0.07,
        marginBottom: 5
    },
    bottomContainer: {
        bottom: 0,
        width: "100%",
        alignItems: "center",
        justifyContent: "center",
        marginBottom: 5
    },
    emptyView: {
        backgroundColor: "white",
        opacity: 0.6,
        padding: 12,
        margin: 5,
        alignItems: "center",
        borderRadius: 12,
    },
});

export default ScheduleScreen;
