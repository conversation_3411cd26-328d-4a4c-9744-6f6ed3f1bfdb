import React, {useState, useEffect} from "react";
import {
    StatusBar,
    Dimensions,
    SafeAreaView,
    View,
    FlatList,
    StyleSheet,
    Text,
    Image,
    TouchableOpacity,
} from "react-native";

import {useNavigation, useRoute} from "@react-navigation/native";
import {ProgramItem} from "../src/interfaces/program";

import {SocialMediaView} from "./Home";
import {Platform} from "react-native";
import {getData} from "../services/asyncStorageService";
import {ProgramConstants} from "../src/utils/constants";

type NavigationProps = {navigation: any};
type ItemProps = {
    time: string;
    program: string;
    isHeading: boolean;
    data: string;
};
type PgmListProps = {programList: ProgramItem[]};

const ScheduleScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();

    const [programList, setProgramList] = useState<ProgramItem[]>([]);

    const items = route.params["programs"];

    const fetchProgramsList = async () => {
        const listFromStorage: ProgramItem[] = await getData("programList");
        setProgramList(listFromStorage);
    };

    useEffect(() => {
        setProgramList(items);
        return () => {
            setProgramList([]);
        };
    }, []);

    return (
        <View style={styles.container}>
            <StatusBar
                animated={true}
                backgroundColor="#DAD6C5"
                barStyle={"light-content"}
            />
            <Image
                style={styles.backgroundImage}
                source={require("../assets/enhancement/program_bg.jpg")}
            />
            <BackIcon navigation={navigation} />
            <SafeAreaView>
                <Image
                    style={styles.logo}
                    source={require("../assets/assets_img_logo.png")}
                />
            </SafeAreaView>
            <PlayerList programList={programList} />
            <View style={styles.souqWaqifView}>
                <Image
                    source={require("../assets/images/souq_waqif.png")}
                    style={styles.souqWaqifImage}
                    resizeMode="contain"
                />
            </View>
            <SocialMediaView />
        </View>
    );
};

const PlayerList = ({programList}: PgmListProps) => {
    return (
        <View style={styles.playerListView}>
            {programList.length > 0 ? (
                <GridComponent programList={programList} />
            ) : (
                <EmptyView />
            )}
        </View>
    );
};

const GridComponent = ({programList}: PgmListProps) => {
    const timeHeader = ProgramConstants.programDurationHead;
    const pgmHeader = ProgramConstants.programNameHead;
    return (
        <View style={{flexDirection: "column"}}>
            <ListTileView
                time={timeHeader}
                program={pgmHeader}
                isHeading={true}
                data=""
            />
            <FlatList
                data={programList}
                // keyExtractor={(item) => item.id}
                renderItem={({item}) => {
                    return (
                        <ListTileView
                            time={
                                item.program_endTime +
                                " - " +
                                item.program_startTime
                            }
                            program={item.program_name}
                            isHeading={false}
                            data=""
                        />
                    );
                }}
            />
        </View>
    );
};

const ListTileView = ({time, program, isHeading}: ItemProps) => {
    const timeObject = isHeading ? time : time;
    return (
        <View style={styles.playerListRow}>
            <ListTileItem
                time={timeObject}
                program={""}
                isHeading={isHeading}
                data={time}
            />
            <ListTileItem
                time={""}
                program={program}
                isHeading={isHeading}
                data={program}
            />
        </View>
    );
};

const ListTileItem = ({data, isHeading}: ItemProps) => {
    return (
        <View style={isHeading ? styles.gridTitle : styles.playerListRowItem}>
            <Text
                style={
                    isHeading ? styles.gridTitleText : styles.pgmNameAndTime
                }>
                {data}
            </Text>
        </View>
    );
};

const EmptyView = () => {
    return (
        <View style={styles.emptyView}>
            <Text style={styles.pgmNameAndTime}>
                البرامج غير متوفرة للتاريخ الحالي
            </Text>
        </View>
    );
};

const BackIcon = ({navigation}: NavigationProps) => {
    return (
        <SafeAreaView>
            <TouchableOpacity
                style={{height: 50, width: 50}}
                testID="BackIcon"
                onPress={() => {
                    //navigate to back
                    console.log("go back");
                    navigation.goBack();
                }}>
                <Image
                    source={require("../assets/back.png")}
                    style={{height: 50, width: 50}}
                />
            </TouchableOpacity>
        </SafeAreaView>
    );
};

const {width, height} = Dimensions.get("window");

const isIos = Platform.OS === "ios";

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    backgroundImage: {
        resizeMode: "stretch",
        width: "100%",
        height: "100%",
        ...StyleSheet.absoluteFillObject,
    },
    logo: {
        alignSelf: "center",
        width: "85%",
        height: height * 0.15,
        resizeMode: "contain",
    },
    pgmNameAndTime: {
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontSize: width * 0.04,
        fontWeight: isIos ? "400" : "800",
        color: "black",
        textAlign: "center",
        alignSelf: "center",
    },
    playerListView: {
        marginTop: "2%",
        alignItems: "center",
        alignSelf: "center",
        justifyContent: "center",
        alignContent: "center",
        overflow: "hidden",
        marginHorizontal: 16,
        height: "50%",
    },
    gridTitle: {
        backgroundColor: "#BF0F2E",
        padding: 8,
        margin: 5,
        alignItems: "center",
        alignSelf: "center",
        justifyContent: "center",
        alignContent: "center",
        borderRadius: 12,
        height: Dimensions.get("window").height * 0.1,
        overflow: "scroll",
        width: Dimensions.get("window").width / 2 - 32,
    },
    gridTitleText: {
        color: "rgb(255, 201, 47)",
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontSize: width * 0.05,
        fontWeight: isIos ? "400" : "800",
        textAlign: "center",
        alignSelf: "center",
    },
    playerListRow: {
        flexDirection: "row",
        overflow: "hidden",
        alignItems: "center",
    },
    playerListRowItem: {
        // minHeight: 40,
        backgroundColor: "white",
        opacity: 0.6,
        padding: 10,
        margin: 5,
        textAlign: "center",
        alignSelf: "center",
        borderRadius: 10,
        overflow: "scroll",
        width: Dimensions.get("window").width / 2 - 32,
    },
    souqWaqifView: {
        marginBottom: "2.5%",
        width: "100%",
        padding: 5,
        resizeMode: "contain",
        borderRadius: 15,
        position: "absolute",
        bottom: height * 0.08,
        alignItems: "center",
    },
    souqWaqifImage: {
        height: height * 0.07,
    },
    emptyView: {
        backgroundColor: "white",
        opacity: 0.6,
        padding: 12,
        margin: 5,
        alignItems: "center",
        borderRadius: 12,
        overflow: "scroll",
    },
});

export default ScheduleScreen;
