import React, {useEffect, useState, useRef} from "react";
import {
    View,
    Text,
    Image,
    StyleSheet,
    StatusBar,
    TouchableOpacity,
    Platform,
    Alert,
    Linking,
    Dimensions,
    ActivityIndicator,
} from "react-native";

import {SafeAreaView, useSafeAreaInsets} from "react-native-safe-area-context";
import TrackPlayer, {
    AppKilledPlaybackBehavior,
    Capability,
    Event,
    State,
    TrackType,
    useTrackPlayerEvents,
} from "react-native-track-player";
import NetInfo from "@react-native-community/netinfo";
import moment from "moment-timezone";

import {AppConstants, TrackPlayerConstants} from "../src/utils/constants";
import {useNavigation} from "@react-navigation/native";
import {ProgramItem} from "../src/interfaces/program";
import apiService from "../services/apiService";
import {storeData, getData} from "../services/asyncStorageService";

const {width, height} = Dimensions.get("window");
const isIos = Platform.OS === "ios";

const Home = () => {
    const navigation = useNavigation();
    const insets = useSafeAreaInsets();

    const [play, setPlay] = useState(false);
    const [isConnected, setIsConnected] = useState(true);
    const [programList, setProgramList] = useState<ProgramItem[]>([]);
    const [currentPlayingProgram, setCurrentProgram] = useState<ProgramItem>();
    const [isDataLoading, setLoading] = useState(true);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const events = [Event.PlaybackError, Event.PlaybackState, Event.RemoteDuck];

    const fetchProgramsList = async (): Promise<ProgramItem[]> => {
        setLoading(true);
        const today = moment.tz("Asia/Qatar").format("DD-MM-YYYY");
        const resData = await apiService.fetchPgmList({program_date: today});
        setLoading(false);
        return resData as ProgramItem[];
    };

    const filterPrograms = (programs: ProgramItem[]) => {
        const qatarTimeString = moment.tz("Asia/Qatar").format("HH:mm:ss");
        const qatarTime = moment.parseZone(qatarTimeString, "HH:mm:ss");

        const currentRunningPrograms = programs.filter((program) => {
            const startTime = moment.parseZone(program.program_startTime, "HH:mm");
            const endTime = moment.parseZone(program.program_endTime, "HH:mm");
            return qatarTime.isBetween(startTime, endTime);
        });

        const futurePrograms = programs.filter((program) => {
            const startTime = moment.parseZone(program.program_startTime, "HH:mm");
            return startTime.isAfter(qatarTime);
        });

        const nextProgram = futurePrograms.length > 0
            ? futurePrograms.reduce((prev, current) =>
                moment.parseZone(prev.program_startTime, "HH:mm").isBefore(moment.parseZone(current.program_startTime, "HH:mm")) ? prev : current
            )
            : null;

        let timeToNextProgram = null;
        if (nextProgram) {
            const nextProgramStartTime = moment.parseZone(nextProgram.program_startTime, "HH:mm");
            timeToNextProgram = nextProgramStartTime.diff(qatarTime);
            if (currentRunningPrograms.length > 0) {
                const currentPgmEndTime = moment.parseZone(currentRunningPrograms[0].program_endTime, "HH:mm");
                const delay = nextProgramStartTime.diff(currentPgmEndTime);
                const diff = delay > 0 ? currentPgmEndTime.diff(qatarTime) : 0;
                timeToNextProgram = diff > 0 ? diff : timeToNextProgram;
            }
        }

        return {currentRunningPrograms, nextProgram, timeToNextProgram};
    };

    useEffect(() => {
        fetchProgramsList().then(initProgramsData);
        return () => {
            intervalRef.current && clearInterval(intervalRef.current);
            setCurrentProgram({} as ProgramItem);
        };
    }, []);

    const initProgramsData = (prgList: ProgramItem[]) => {
        setProgramList(prgList);
        storeData("programList", prgList);
        updateProgramTitle();
    };

    const updateProgramTitle = async () => {
        const programs = await getData("programList");
        const {currentRunningPrograms, nextProgram, timeToNextProgram} = filterPrograms(programs);

        setCurrentProgram(currentRunningPrograms.length > 0 ? currentRunningPrograms[0] : {} as ProgramItem);

        if (intervalRef.current) clearInterval(intervalRef.current);

        if (nextProgram && timeToNextProgram) {
            intervalRef.current = setInterval(updateProgramTitle, Number(timeToNextProgram) + 15000);
        }
    };

    useEffect(() => {
        setupPlayer();
        const unsubscribe = NetInfo.addEventListener((state) => {
            setIsConnected(state.isConnected);
        });

        return () => {
            TrackPlayer.stop();
            unsubscribe();
        };
    }, []);

    useTrackPlayerEvents(events, (event) => {
        if (Platform.OS === "ios" && "paused" in event && event.type === Event.RemoteDuck) {
            setPlay(false);
        }
        if (event.type === Event.PlaybackError) {
            TrackPlayer?.play();
        }
        if ("state" in event) {
            if (event?.state === State?.Paused) {
                setPlay(false);
            } else if (event?.state === State?.Playing) {
                setPlay(true);
                TrackPlayer.updateOptions({
                    android: {
                        appKilledPlaybackBehavior: AppKilledPlaybackBehavior.StopPlaybackAndRemoveNotification,
                        alwaysPauseOnInterruption: true,
                    },
                    capabilities: [Capability.Play, Capability.Pause],
                    compactCapabilities: [Capability.Play, Capability.Pause],
                    notificationCapabilities: [Capability.Play, Capability.Pause],
                    progressUpdateEventInterval: 2,
                });
            }
        }
    });

    const setupPlayer = async () => {
        try {
            await TrackPlayer.setupPlayer({autoHandleInterruptions: true});
            await TrackPlayer.add({
                id: TrackPlayerConstants.trackPlayerId,
                url: AppConstants.apiUrl,
                title: TrackPlayerConstants.audioTitle,
                artist: TrackPlayerConstants.artist,
                type: TrackType.HLS,
                artwork: require("../assets/assets_img_logo.png"),
            });
        } catch (error) {
            console.error("Error setting up player:", error);
        }
    };

    const playAudio = async () => {
        if (!isConnected) {
            Alert.alert(AppConstants.networkAlert.title, AppConstants.networkAlert.message, [{text: "OK"}]);
            setPlay(false);
        } else {
            play ? await TrackPlayer.pause() : await TrackPlayer.play();
            setPlay(!play);
        }
    };

    const onPodcastSelection = async () => {
        fetchProgramsList().then((prgList) => {
            initProgramsData(prgList);
            navigation.navigate("Schedule", {programs: prgList});
        });
    };

    return (
        <View style={styles.container}>
            <StatusBar animated translucent backgroundColor="#DAD6C5" barStyle="light-content" />
            <Image style={styles.backgroundImage} source={require("../assets/assets_img_livepagebg.png")} />

            <SafeAreaView>
                <Image style={styles.logo} source={require("../assets/assets_img_logo.png")} />
            </SafeAreaView>

            <View style={styles.buttonContainer}>
                <Text style={styles.listenNowText}>استمع الآن</Text>
                <TouchableOpacity style={styles.playPauseButton} onPress={playAudio}>
                    <Image
                        source={
                            play
                                ? require("../assets/assets_img_stopicon.png")
                                : require("../assets/assets_img_playicon.png")
                        }
                        style={styles.buttonImage}
                    />
                </TouchableOpacity>
                <Text style={styles.textBelowButton}>106.60</Text>
                <Text style={styles.fmText}>FM إف إم</Text>
                <TouchableOpacity style={styles.playPauseButton} onPress={onPodcastSelection}>
                    <Image
                        source={require("../assets/enhancement/radiopro_icon.png")}
                        style={styles.buttonImage}
                    />
                </TouchableOpacity>
                <Text style={styles.fmText}>برامجنا اليوم</Text>
                <CurrentPlayingInfo program={currentPlayingProgram} />
            </View>

            <SafeAreaView style={[styles.bottomContainer, {paddingBottom: insets.bottom || 10}]}>
                <View style={styles.souqWaqifView}>
                    <Image
                        source={require("../assets/images/souq_waqif.png")}
                        style={styles.souqWaqifImage}
                        resizeMode="contain"
                    />
                </View>
                <SocialMediaView />
            </SafeAreaView>

            {isDataLoading && <LoadingIndicator />}
        </View>
    );
};

const CurrentPlayingInfo = ({program}: {program: ProgramItem | undefined}) => {
    const pgmName = program?.program_name || "";
    return (
        <View style={styles.currentPlayingInfoView}>
            <View style={styles.currentPlayingSongView}>
                <Text style={styles.currentPlayingSongText}>{pgmName}</Text>
            </View>
            <View style={styles.streamingNowView}>
                <Text style={styles.streamingNowText}>البث الآن</Text>
            </View>
        </View>
    );
};

const LoadingIndicator = () => (
    <View style={styles.loadingOverlay}>
        <ActivityIndicator size={60} color="#318CE7" />
    </View>
);

export const SocialMediaView = () => {
    const icons = [
        {id: 'fb', url: AppConstants.externalLinks.fbUrl, image: require("../assets/enhancement/facebook.png")},
        {id: 'tw', url: AppConstants.externalLinks.twitterUrl, image: require("../assets/enhancement/twitter.png")},
        {id: 'insta', url: AppConstants.externalLinks.instalUrl, image: require("../assets/enhancement/instagram.png")},
        {id: 'yt', url: AppConstants.externalLinks.youtubeUrl, image: require("../assets/enhancement/youtube.png")},
        {id: 'pod', url: AppConstants.externalLinks.podCastUrl, image: require("../assets/enhancement/spotify.png")},
    ];

    return (
        <View style={styles.socialIconContainer}>
            {icons.map((item) => (
                <TouchableOpacity key={item.id} onPress={() => Linking.openURL(item.url)}>
                    <Image source={item.image} style={styles.bottomIcons} />
                </TouchableOpacity>
            ))}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        paddingTop: Platform.OS === "android" ? StatusBar.currentHeight : 0,
        flex: 1,
    },
    backgroundImage: {
        resizeMode: "stretch",
        width: "100%",
        height: "100%",
        ...StyleSheet.absoluteFillObject,
    },
    logo: {
        alignSelf: "center",
        width: "85%",
        height: height * 0.15,
        resizeMode: "contain",
    },
    listenNowText: {
        color: "#BF0F2E",
        fontSize: width * 0.05,
        textAlign: "center",
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontWeight: Platform.OS === "ios" ? "bold" : "normal",
    },
    playPauseButton: {
        borderRadius: 50,
        alignSelf: "center",
        marginTop: "2%",
        marginBottom: 10,
    },
    buttonImage: {
        height: height * 0.1,
        width: height * 0.1,
        resizeMode: "contain",
    },
    textBelowButton: {
        color: "#BF0F2E",
        fontSize: width * 0.045,
        textAlign: "center",
        fontFamily: "assets_fonts_dinnextltarabicbold",
    },
    fmText: {
        color: "#BF0F2E",
        fontSize: width * 0.04,
        marginTop: 2,
        textAlign: "center",
        fontFamily: "assets_fonts_dinnextltarabicbold",
    },
    buttonContainer: {
        justifyContent: "center",
        alignItems: "center",
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    currentPlayingInfoView: {
        width: width * 0.75,
        flexDirection: "row",
        marginTop: 20,
        alignSelf: "center",
    },
    currentPlayingSongView: {
        backgroundColor: "white",
        width: "70%",
        justifyContent: "center",
        marginRight: 5,
        padding: 1,
    },
    currentPlayingSongText: {
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontSize: width * 0.035,
        paddingHorizontal: 8,
        color: "black",
        fontWeight: isIos ? "400" : "800",
        textAlign: "center",
    },
    streamingNowView: {
        backgroundColor: "rgb(255, 201, 47)",
        width: "30%",
        justifyContent: "center",
        padding: 1,
    },
    streamingNowText: {
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontSize: width * 0.035,
        color: "#BF0F2E",
        fontWeight: isIos ? "400" : "800",
        textAlign: "center",
    },
    bottomContainer: {
        position: "absolute",
        bottom: 0,
        width: "100%",
        alignItems: "center",
        justifyContent: "center",
    },
    souqWaqifView: {
        marginBottom: 10,
        alignItems: "center",
    },
    souqWaqifImage: {
        height: height * 0.07,
        resizeMode: "contain",
    },
    socialIconContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        width: width * 0.75,
        alignItems: "center",
    },
    bottomIcons: {
        height: width * 0.1,
        width: width * 0.1,
        resizeMode: "contain",
        marginHorizontal: 5
    },
    loadingOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: "rgba(0,0,0,0.5)",
        justifyContent: "center",
        alignItems: "center",
    },
});

export default Home;
