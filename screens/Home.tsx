import React, {useEffect, useState, useRef} from "react";
import {
    View,
    Text,
    Image,
    StyleSheet,
    StatusBar,
    TouchableOpacity,
    Platform,
    Alert,
    Linking,
    Dimensions,
} from "react-native";
import TrackPlayer, {
    AppKilledPlaybackBehavior,
    Capability,
    Event,
    State,
    TrackType,
    useTrackPlayerEvents,
} from "react-native-track-player";

import NetInfo from "@react-native-community/netinfo";

import {AppConstants, TrackPlayerConstants} from "../src/utils/constants";
import {useNavigation} from "@react-navigation/native";
import {ProgramItem} from "../src/interfaces/program";
import apiService from "../services/apiService";
import {ActivityIndicator} from "react-native";
import {SafeAreaView} from "react-native";
import {storeData, getData} from "../services/asyncStorageService";
import moment from "moment-timezone";

type PgmItemProps = {program: undefined | ProgramItem};

const Home = () => {
    const navigation = useNavigation();

    const [play, setPlay] = useState(false);
    const [isConnected, setIsConnected] = useState(true);

    const [programList, setProgramList] = useState<ProgramItem[]>([]);
    const [currentPlayingProgram, setCurrentProgram] = useState<ProgramItem>();
    const [isDataLoading, setLoading] = useState(true);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const events = [
        Event?.PlaybackError,
        Event?.PlaybackState,
        Event?.RemoteDuck,
    ];

    // function to call Get Programs API
    const fetchProgramsList = async (): Promise<ProgramItem[]> => {
        setLoading(true);
        const today = moment.tz("Asia/Qatar").format("DD-MM-YYYY");
        const data = {program_date: today};
        const resData = await apiService.fetchPgmList(data);

        const responseData = resData;
        const listTemp: ProgramItem[] = responseData as ProgramItem[];
        setLoading(false);

        return listTemp;
    };

    // function to get current program running and the next program for the system date and time
    const filterPrograms = (programs: ProgramItem[]) => {
        // const currentTime = qatarMoment();
        // Make sure you have moment-timezone included

        // Get the current time in Qatar time zone
        const qatarTimeString = moment.tz("Asia/Qatar").format("HH:mm:ss");
        const qatarTime = moment.parseZone(qatarTimeString, "HH:mm:ss");

        console.log("Current Qatar Time:", qatarTime);

        // Find the current running programs
        const currentRunningPrograms = programs.filter((program) => {
            const startTime = moment.parseZone(
                program.program_startTime,
                "HH:mm",
            );
            const endTime = moment.parseZone(program.program_endTime, "HH:mm");
            return qatarTime.isBetween(startTime, endTime);
        });

        // Find the next program
        const futurePrograms = programs.filter((program) => {
            const startTime = moment.parseZone(
                program.program_startTime,
                "HH:mm",
            );
            return startTime.isAfter(qatarTime);
        });

        const nextProgram =
            futurePrograms.length > 0
                ? futurePrograms.reduce((prev, current) => {
                    return moment
                        .parseZone(prev.program_startTime, "HH:mm")
                        .isBefore(
                            moment.parseZone(
                                current.program_startTime,
                                "HH:mm",
                            ),
                        )
                        ? prev
                        : current;
                })
                : null;

        let timeToNextProgram = null;
        if (nextProgram) {
            const nextProgramStartTime = moment.parseZone(
                nextProgram.program_startTime,
                "HH:mm",
            );
            timeToNextProgram = nextProgramStartTime.diff(qatarTime);
            if (currentRunningPrograms.length > 0) {
                const currentPgmEndTime = moment.parseZone(
                    currentRunningPrograms[0].program_endTime,
                    "HH:mm",
                );
                const delayBeforeNextProgramStarts =
                    nextProgramStartTime.diff(currentPgmEndTime);
                const diffBwDelayAndCurrentTime =
                    delayBeforeNextProgramStarts > 0
                        ? currentPgmEndTime.diff(qatarTime)
                        : 0;
                timeToNextProgram =
                    diffBwDelayAndCurrentTime > 0
                        ? diffBwDelayAndCurrentTime
                        : timeToNextProgram;
            }
        }
        return {currentRunningPrograms, nextProgram, timeToNextProgram};
    };

    // call the get programs fn in form load and call the update program name fn
    useEffect(() => {
        fetchProgramsList().then((prgList) => {
            initProgramsData(prgList);
        });
        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
            }
            setCurrentProgram({} as ProgramItem);
        };
    }, []);

    const initProgramsData = (prgList: ProgramItem[]) => {
        setProgramList(prgList);
        storeData("programList", prgList); // Update local storage after state update
        updateProgramTitle();
    };

    // here the current program=, if any, will be set to state and new timer will set for next program start date
    const updateProgramTitle = async () => {
        const programs = await getData("programList");
        const {currentRunningPrograms, nextProgram, timeToNextProgram} =
            filterPrograms(programs);
        console.log("Current Running Programs:", currentRunningPrograms);
        console.log("Next Program:", nextProgram);
        console.log(
            "Time to next program (in minutes):",
            (Number(timeToNextProgram) + 15000) / 60000,
        );

        if (currentRunningPrograms.length > 0) {
            setCurrentProgram(currentRunningPrograms[0]);
        } else {
            setCurrentProgram({} as ProgramItem);
        }

        // Clear previous interval if it exists
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
        }

        // Set timer for the next program start date
        if (nextProgram !== null && nextProgram !== undefined) {
            intervalRef.current = setInterval(
                updateProgramTitle,
                Number(timeToNextProgram) + 15000,
            );
        }
    };

    // TODO: Remove later
    // // here the current program=, if any, will be set to state and new timer will set for next program start date
    // const updateProgramTitle = (async () => {
    //     const programs = await getData("programList");
    //     const { currentRunningPrograms, nextProgram, timeToNextProgram } = filterPrograms(programs);
    //     console.log("Current Running Programs:", currentRunningPrograms);
    //     console.log("Next Program:", nextProgram);
    //     console.log("time to next program", (Number(timeToNextProgram) + 15000)/60000);

    //     if (currentRunningPrograms.length > 0) {
    //         setCurrentProgram(currentRunningPrograms[0]);
    //     } else {
    //         setCurrentProgram({} as ProgramItem);
    //     }

    //     // start timer and set for interval to next program start date
    //     if (nextProgram !== null && nextProgram !== undefined) {

    //         const intervalId = setInterval(updateProgramTitle, Number(timeToNextProgram)+15000);

    //         // TODO: this code needs to be verified
    //         return () => {
    //             setProgramList([]);
    //             clearInterval(intervalId);
    //         };
    //     }
    // });

    useEffect(() => {
        setupPlayer();
        const unsubscribe = NetInfo?.addEventListener((state) => {
            setIsConnected(state.isConnected);
        });

        return () => {
            TrackPlayer.stop();
            unsubscribe();
        };
    }, []);

    useTrackPlayerEvents(events, (event) => {
        if (
            Platform.OS === "ios" &&
            "paused" in event &&
            event.type === Event.RemoteDuck
        ) {
            setPlay(false);
        }
        if (event.type === Event.PlaybackError) {
            // setPlay(false);
            TrackPlayer?.play();
        }
        if ("state" in event) {
            if (event?.state === State?.Paused) {
                setPlay(false);
            } else if (event?.state === State?.Playing) {
                setPlay(true);
                TrackPlayer?.updateOptions({
                    android: {
                        appKilledPlaybackBehavior: AppKilledPlaybackBehavior?.StopPlaybackAndRemoveNotification,
                        alwaysPauseOnInterruption: true,
                    },
                    capabilities: [Capability.Play, Capability.Pause],
                    compactCapabilities: [Capability.Play, Capability.Pause],
                    notificationCapabilities: [Capability.Play, Capability.Pause],
                    progressUpdateEventInterval: 2,
                });
            }
        }
    });

    // Add a back button listener for handling playback control
    const setupPlayer = async () => {
        try {
            await TrackPlayer?.setupPlayer({
                autoHandleInterruptions: true,
            });
            await TrackPlayer.add({
                id: TrackPlayerConstants.trackPlayerId,
                url: AppConstants.apiUrl,
                title: TrackPlayerConstants.audioTitle,
                artist: TrackPlayerConstants.artist,
                type: TrackType?.HLS,
                artwork: require("../assets/assets_img_logo.png"),
            });
        } catch (error) {
            console.error("Error setting up player:", error);
        }
    };

    const playAudio = async () => {
        console.log("iSConnected", isConnected);
        if (!isConnected) {
            Alert.alert(
                AppConstants.networkAlert.title,
                AppConstants.networkAlert.message,
                [
                    {
                        text: "OK",
                        onPress: () => {
                            console.log("Tap ok");
                        },
                    },
                ],
            );
            setPlay(false);
        } else {
            if (play) {
                await TrackPlayer?.pause();
            } else {
                await TrackPlayer?.play();
            }
            setPlay(!play);
        }
    };

    const onPodcastSelection = async () => {
        fetchProgramsList().then((prgList) => {
            initProgramsData(prgList);
            navigation.navigate("Schedule", {programs: prgList});
        });
    };

    return (
        <View style={styles.container}>
            <StatusBar
                animated={true}
                backgroundColor="#DAD6C5"
                barStyle={"light-content"}
            />
            <Image
                style={styles.backgroundImage}
                source={require("../assets/assets_img_livepagebg.png")}
            />
            <SafeAreaView>
                <Image
                    style={styles.logo}
                    source={require("../assets/assets_img_logo.png")}
                />
            </SafeAreaView>
            <View style={styles.buttonContainer}>
                <Text style={styles.listenNowText}>استمع الآن</Text>
                <TouchableOpacity
                    style={styles.playPauseButton}
                    testID="play-button"
                    onPress={playAudio}>
                    <Image
                        source={
                            play
                                ? require("../assets/assets_img_stopicon.png")
                                : require("../assets/assets_img_playicon.png")
                        }
                        style={styles.buttonImage}
                    />
                </TouchableOpacity>
                <Text style={styles.textBelowButton}>106.60</Text>
                <Text style={styles.fmText}>FM إف إم</Text>
                <TouchableOpacity
                    style={styles.playPauseButton}
                    testID="podcast"
                    onPress={() => onPodcastSelection()}>
                    <Image
                        source={require("../assets/enhancement/radiopro_icon.png")}
                        style={styles.buttonImage}
                    />
                </TouchableOpacity>
                <Text style={styles.fmText}>برامجنا اليوم</Text>
                <CurrentPlayingInfo program={currentPlayingProgram} />
            </View>
            <View style={styles.souqWaqifView}>
                <Image
                    source={require("../assets/images/souq_waqif.png")}
                    style={styles.souqWaqifImage}
                    resizeMode="contain"
                />
            </View>
            <SocialMediaView />
            {isDataLoading && <LoadingIndicator />}
        </View>
    );
};

const CurrentPlayingInfo = ({program}: PgmItemProps) => {
    const programItem = program;
    let pgmName = "";
    if (programItem) {
        pgmName = programItem.program_name;
    }

    return (
        <View style={styles.currentPlayingInfoView}>
            <View style={styles.currentPlayingSongView}>
                <Text style={styles.currentPlayingSongText}>{pgmName}</Text>
            </View>
            <View style={styles.streamingNowView}>
                <Text style={styles.streamingNowText}>البث الآن</Text>
            </View>
        </View>
    );
};

const LoadingIndicator = () => {
    return (
        <View style={styles.loadingOverlay}>
            <ActivityIndicator size={60} color="#318CE7" />
        </View>
    );
};

export const SocialMediaView = () => {
    return <View style={styles.roundedView}>{renderSocialMediaIcons()}</View>;
};

const renderSocialMediaIcons = () => {
    const socialMediaLinks = [
        {
            id: AppConstants.socialMediaOptions.fb,
            url: AppConstants?.externalLinks.fbUrl,
            image: require("../assets/enhancement/facebook.png"),
        },
        {
            id: AppConstants.socialMediaOptions.twitter,
            url: AppConstants.externalLinks.twitterUrl,
            image: require("../assets/enhancement/twitter.png"),
        },
        {
            id: AppConstants.socialMediaOptions.instagram,
            url: AppConstants?.externalLinks.instalUrl,
            image: require("../assets/enhancement/instagram.png"),
        },
        {
            id: AppConstants.socialMediaOptions.youtube,
            url: AppConstants?.externalLinks.youtubeUrl,
            image: require("../assets/enhancement/youtube.png"),
        },
        {
            id: AppConstants.socialMediaOptions.podcast,
            url: AppConstants?.externalLinks.podCastUrl,
            image: require("../assets/enhancement/spotify.png"),
        },
    ];

    return socialMediaLinks.map((link) => (
        <TouchableOpacity
            key={link.id}
            style={styles.bottomIconView}
            testID={link.id}
            onPress={() => {
                Linking.openURL(link.url);
            }}>
            <Image source={link.image} style={styles.bottomIcons} />
        </TouchableOpacity>
    ));
};
const {width, height} = Dimensions.get("window");
const isIos = Platform.OS === "ios";

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    backgroundImage: {
        resizeMode: "stretch",
        width: "100%",
        height: "100%",
        ...StyleSheet.absoluteFillObject,
    },
    logo: {
        top: 20,
        alignSelf: "center",
        width: "85%",
        height: height * 0.15,
        // backgroundColor: 'white',
        resizeMode: "contain",
    },
    listenNowText: {
        textAlign: "center",
        alignSelf: "center",
        color: "#BF0F2E",
        fontSize: width * 0.05,
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontWeight: Platform.OS === "ios" ? "bold" : "normal",
    },
    playPauseButton: {
        borderRadius: 50,
        alignSelf: "center",
        marginTop: "2%",
        marginBottom: 10,
    },
    buttonText: {
        color: "white",
        fontSize: 18,
        fontWeight: "bold",
    },
    buttonImage: {
        height: height * 0.1,
        width: height * 0.1,
    },
    textBelowButton: {
        textAlign: "center",
        color: "#BF0F2E",
        // lineHeight: 25,
        fontSize: width * 0.045,
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontWeight: Platform.OS === "ios" ? "bold" : "normal",
    },
    fmText: {
        textAlign: "center",
        color: "#BF0F2E",
        // lineHeight: 20,
        fontSize: width * 0.04,
        marginTop: 2,
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontWeight: Platform.OS === "ios" ? "bold" : "normal",
    },
    textBelowBottomButton: {
        color: "black",
        alignSelf: "center",
        fontSize: width * 0.05,
        textAlign: "center",
        position: "absolute",
        bottom: 30,
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontWeight: Platform.OS === "ios" ? "bold" : "normal",
    },
    buttonContainer: {
        justifyContent: "center",
        alignItems: "center",
        position: "absolute",
        flex: 1,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    currentPlayingInfoView: {
        width: width * 0.75,
        textAlign: "center",
        alignSelf: "center",
        justifyContent: "center",
        alignContent: "center",
        alignItems: "center",
        flexDirection: "row",
        marginTop: 20,
    },
    currentPlayingSongView: {
        backgroundColor: "white",
        height: "100%",
        width: "70%",
        justifyContent: "center",
        marginRight: 5,
        padding: 1,
        flexDirection: "row",
    },
    currentPlayingSongText: {
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontSize: width * 0.035,
        flex: 1,
        paddingHorizontal: 8,
        paddingVertical: 3,
        color: "black",
        fontWeight: isIos ? "400" : "800",
        textAlign: "center",
    },
    streamingNowView: {
        backgroundColor: "rgb(255, 201, 47)",
        padding: 1,
        width: "30%",
        justifyContent: "center",
        flexDirection: "row",
    },
    streamingNowText: {
        fontFamily: "assets_fonts_dinnextltarabicbold",
        fontSize: width * 0.035,
        color: "#BF0F2E",
        fontWeight: isIos ? "400" : "800",
    },
    souqWaqifView: {
        marginBottom: "2%",
        width: "100%",
        padding: 5,
        resizeMode: "contain",
        borderRadius: 15,
        position: "absolute",
        bottom: height * 0.09,
        alignItems: "center",
    },
    souqWaqifImage: {
        height: height * 0.07,
    },
    roundedView: {
        flexDirection: "row",
        width: width * 0.75,
        justifyContent: "space-between",
        alignItems: "center",
        alignSelf: "center",
        position: "absolute",
        bottom: "1%",
    },
    bottomIconView: {
        marginTop: 5,
        width: width * 0.15,
        height: width * 0.15,
        resizeMode: "contain",
        borderRadius: 15,
        alignItems: "center",
        justifyContent: "center",
    },
    bottomIcons: {
        height: width * 0.1,
        width: width * 0.1,
        resizeMode: "contain",
        borderRadius: 8,
    },
    loadingOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        justifyContent: "center",
        alignItems: "center",
    },
});

export default Home;
