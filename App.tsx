import React from "react";

import Home from "./screens/Home";
import { SafeAreaProvider } from "react-native-safe-area-context";

import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";

import ScheduleScreen from "./screens/ScheduleScreen";

const Stack = createStackNavigator();

const App = () => {
    return (
        <NavigationContainer>
            <Stack.Navigator screenOptions={{ headerShown: false }}>
                <Stack.Screen name="Home" component={MainComponent} />
                <Stack.Screen name="Schedule" component={ScheduleScreen} />
            </Stack.Navigator>
        </NavigationContainer>
    );
};


const MainComponent = () => (
    <SafeAreaProvider>
        <Home />
    </SafeAreaProvider>
);

export default App;
