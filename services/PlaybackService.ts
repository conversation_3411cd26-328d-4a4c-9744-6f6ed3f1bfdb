import TrackPlayer, {Event} from "react-native-track-player";

const PlaybackService = async () => {
    TrackPlayer.addEventListener(Event.RemotePause, () => TrackPlayer.pause());
    TrackPlayer.addEventListener(Event.RemotePlay, () => TrackPlayer.play());
    TrackPlayer.addEventListener(Event.RemoteDuck, (data) =>
        console.log("-----------", data),
    );
};

export default PlaybackService;
