import axios from "axios";
import { BASE_URL, PROGRAMS_LIST, REQUEST_METHOD_POST } from "../src/utils/apiConstants";

class ApiService {

    fetchPgmList = async (reqBody) => {

        const url = BASE_URL + PROGRAMS_LIST;


        try {
            const response = await axios({
                method: REQUEST_METHOD_POST,
                url: url,
                data: reqBody,
                validateStatus: () => true
            });
            const resData = response.data;

            if (response.status === 202) {
                return resData;
            } else {
                return resData;
            }
        } catch (err) {
            return [];
        }


    };


}

const apiService = new ApiService();
export default apiService;