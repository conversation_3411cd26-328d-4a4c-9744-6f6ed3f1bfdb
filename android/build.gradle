buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 21
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.9.0"
        androidXCore = "1.0.2"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.1.4")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

apply plugin: "com.facebook.react.rootproject"
