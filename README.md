# Sout Al Islam App

Sout Al Islam Hybrid Mobile App
Application is built using React Native.

## Screens

- Splash screens
- Home Screen [Play button screen]

## Setup instructions

### 1. Install dependencies

```
mkdir sout-al-islam
cd sout-al-islam
git clone https://git.citrusinformatics.com/RGB/al-rayyan/sout-al-islam-hybrid-mobile-app.git
cd sout-al-islam
npm install
```

### 2. Run the app 

Setup for iOS
```
cd ios
pod install
```
Run in iOS device
```
npx react-native run-ios "<Device name>"
```

Run in Android
```
npx react-native run-android
```