{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/masked-view": "^0.1.11", "@react-native-community/netinfo": "^6.2.1", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@testing-library/react-native": "^12.4.2", "@types/react-native-vector-icons": "^6.4.18", "axios": "^1.7.2", "dayjs": "^1.11.11", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "react": "18.2.0", "react-native": "0.73.0", "react-native-bootsplash": "^6.3.10", "react-native-gesture-handler": "^2.17.1", "react-native-reanimated": "^3.6.3", "react-native-rename": "^3.2.13", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-track-player": "^4.1.1", "react-native-vector-icons": "^10.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "^0.73.18", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@testing-library/jest-native": "^5.4.3", "@types/jest": "^29.5.11", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.55.0", "jest": "^29.7.0", "prettier": "2.8.8", "react-native-asset": "^2.1.1", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}