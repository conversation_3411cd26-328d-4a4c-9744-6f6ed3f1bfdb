import React from "react";
import {render, act, fireEvent} from "@testing-library/react-native";
import {Alert} from "react-native";
import renderer from "react-test-renderer";
import Home from "../screens/Home";
import NetInfo from "@react-native-community/netinfo";
import {AppConstants} from "../utils/constants";
import TrackPlayer from "react-native-track-player";
console.error = jest.fn();

describe("Home component", () => {
    it("renders correctly", async () => {
        const tree = renderer.create(<Home />);
        expect(tree).toMatchSnapshot();
    });
    test("should show play button", () => {
        const {getByTestId} = render(<Home />);
        const playButton = getByTestId("play-button");
        expect(playButton).toBeTruthy();
    });

    it("should handle play/pause correctly with network connection", async () => {
        // Mock network connection as connected
        (NetInfo.fetch as jest.Mock).mockResolvedValue({isConnected: true});

        const {getByTestId} = render(<Home />);
        await act(async () => {
            fireEvent(getByTestId("play-button"), "press");
        });

        // Assert TrackPlayer.play() is called
        expect(TrackPlayer.play).toHaveBeenCalled();
    });

    it("should handle play/pause correctly without network connection", async () => {
        // Mock network connection as not connected
        (NetInfo.fetch as jest.Mock).mockResolvedValue({isConnected: false});

        // expect(TrackPlayer.play).not.toHaveBeenCalled();
        const {getByTestId} = render(<Home />);
        try {
            fireEvent(getByTestId("play-button"), "press");
            expect(TrackPlayer.play).not.toHaveBeenCalled();
            expect(Alert.alert).toHaveBeenCalledWith(
                AppConstants.networkAlert.title,
                AppConstants.networkAlert.message,
                expect.any(Array),
            );
        } catch {
            console.log("catching error");
        }
    });
});
