<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="428" height="926"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="assets_img_intropage" translatesAutoresizingMaskIntoConstraints="NO" id="als-Lx-fsc">
                                <rect key="frame" x="0.0" y="-12" width="428" height="938"/>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="als-Lx-fsc" firstAttribute="top" secondItem="Bcu-3y-fUS" secondAttribute="top" constant="-59" id="3Ge-9r-ort"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="trailing" secondItem="als-Lx-fsc" secondAttribute="trailing" id="EIP-F7-OW4"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="bottom" secondItem="als-Lx-fsc" secondAttribute="bottom" constant="-34" id="NFt-ft-Had"/>
                            <constraint firstItem="als-Lx-fsc" firstAttribute="leading" secondItem="Bcu-3y-fUS" secondAttribute="leading" id="fAW-SJ-XpH"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="51.627906976744185" y="374.67811158798287"/>
        </scene>
    </scenes>
    <resources>
        <image name="assets_img_intropage" width="1080" height="1920"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
