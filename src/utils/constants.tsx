import {Platform} from "react-native";

export const AppConstants = {
    apiUrl: "https://alrayyancdn.vidgyor.com/pub-nooldraybinbdh/audioonly/playlist.m3u8",
    networkAlert: {
        title: "Network Status",
        message: "Connection Lost! Please check your internet and try again.",
    },
    networkPlayerErrorAlert: {
        title: "Network Status",
        message: "An error occurred during streaming.",
    },
    externalLinks: {
        fbUrl: "https://www.facebook.com/SoutAlislam.Qa/",
        instalUrl: "https://www.instagram.com/soutalislam.qa/",
        twitterUrl: "https://twitter.com/SoutAlislamQa",
        youtubeUrl: "https://www.youtube.com/@SoutAlislamQa",
        podCastUrl:
            Platform.OS === "ios"
                ? "https://podcasts.apple.com/qa/podcast/إذاعة-صوت-الإسلام/id1679937376"
                : "https://podcasts.google.com/feed/aHR0cHM6Ly9hbmNob3IuZm0vcy9kZGRlYzNmYy9wb2RjYXN0L3Jzcw",
    },
    socialMediaOptions: {
        fb: "Facebook",
        twitter: "Twitter",
        instagram: "Instagram",
        youtube: "YouTube",
        podcast: "podcast",
    },
};

export const TrackPlayerConstants = {
    trackPlayerId: "1",
    url: AppConstants.apiUrl,
    audioTitle: "Sawt Al Islam",
    artist: "106FM",
};

export const ProgramConstants = {
    programDurationHead: "توقيت\n(توقيت الدوحة)", //"Timing/n(Doha Time)"
    programNameHead: "البرنامج",
    pm: "مساءً",
    am: "صباحًا",
};

export const NavigationConstants = {
    scheduleScreen: "Schedule",
    homeScreen: "Home",
};

export const storageConstants = {
    ProgramListKey: "programList",
};
