

export function convertToTimeObj(timeString) {
    // Split the string into hours and minutes
    const [hours, minutes] = timeString.split(':');

    // Convert hours and minutes to numbers
    const hour = parseInt(hours, 10);
    const minute = parseInt(minutes, 10);

    // Validate input format (optional)
    if (isNaN(hour) || isNaN(minute) || minute < 0 || minute > 59) {
        throw new Error('Invalid time format. Please use HH:MM format.');
    }

    // Calculate AM/PM
    const amPm = hour < 12 ? 'AM' : 'PM';

    // Adjust hours for 12-hour format (if necessary)
    const adjustedHour = hour === 0 ? 12 : (hour > 12 ? hour - 12 : hour);

    // Create the time object
    const timeObject = {
        hours: adjustedHour,
        minutes: minute,
        amPm: amPm,
    };
    return timeObject;

}


export function convertTimeString(timeString) {

    // Create the time object
    const timeObject = convertToTimeObj(timeString);
    // Format the time string with AM/PM
    const formattedString = `${timeObject.hours.toString().padStart(2, '0')}:${timeObject.minutes.toString().padStart(2, '0')} ${timeObject.amPm}`;

    return formattedString;
}
