
export interface ProgramList {
    dataItem: ProgramItem[]
}


export interface ProgramItem {
    _id: number;
    program_name: string;
    program_url: string;
    program_date: string;
    program_startTime: string;
    program_endTime: string;
}


const pgmObject: ProgramItem = {
    _id: 0,
    program_name: "",
    program_url: "",
    program_date: "",
    program_startTime: "",
    program_endTime: ""
};